#!/usr/bin/env python3
"""
Test the enhanced extraction methods for momentum factors
"""

import json
from enhanced_gemini_integration import EnhancedGeminiAnalyzer

def test_extraction_methods():
    """Test the enhanced extraction methods with real data"""
    print("🧪 TESTING ENHANCED EXTRACTION METHODS")
    print("=" * 60)
    
    # Initialize analyzer
    analyzer = EnhancedGeminiAnalyzer()
    
    # Load real serve patterns from prediction history
    try:
        with open('prediction_history.json', 'r') as f:
            predictions = json.load(f)
        
        if not predictions:
            print("No prediction data found")
            return
        
        # Get the most recent prediction's serve patterns
        recent_pred = predictions[-1]
        serve_patterns = recent_pred.get('serve_patterns', {})
        
        print(f"Testing with recent prediction:")
        print(f"Players: {recent_pred['player1_name']} vs {recent_pred['player2_name']}")
        print(f"Score: {recent_pred['score']}")
        
        # Test extraction for each player
        for player_code, pattern in serve_patterns.items():
            print(f"\n{player_code} Extraction Test:")
            print("-" * 30)
            
            # Test momentum intensity extraction
            momentum_intensity = analyzer._get_momentum_intensity(pattern)
            print(f"  Momentum Intensity: {momentum_intensity:.1f}")
            
            # Test service consistency extraction
            service_consistency = analyzer._get_service_consistency(pattern)
            print(f"  Service Consistency: {service_consistency:.1f}")
            
            # Test mental fatigue extraction
            mental_fatigue = analyzer._get_mental_fatigue(pattern)
            print(f"  Mental Fatigue: {mental_fatigue:.1f}%")
            
            # Test clutch performance extraction
            clutch_performance = analyzer._get_clutch_performance(pattern)
            print(f"  Clutch Performance: {clutch_performance:.1f}%")
            
            # Show what we're extracting from
            print(f"\n  Raw Data Sources:")
            if 'momentum_intensity' in pattern:
                print(f"    Momentum: {str(pattern['momentum_intensity'])[:80]}...")
            if 'pressure_metrics' in pattern:
                print(f"    Pressure: {str(pattern['pressure_metrics'])[:80]}...")
            if 'serving_rhythm' in pattern:
                print(f"    Rhythm: {str(pattern['serving_rhythm'])[:80]}...")
        
        # Test momentum factors calculation
        print(f"\n\nMOMENTUM FACTORS CALCULATION:")
        print("=" * 40)
        
        player_codes = list(serve_patterns.keys())
        if len(player_codes) >= 2:
            momentum_factors = analyzer._extract_momentum_factors(
                serve_patterns, player_codes[0], player_codes[1]
            )
            
            print(f"Calculated momentum factors:")
            for factor, value in momentum_factors.items():
                print(f"  {factor}: {value:.2f}")
            
            # Check if we've improved from zeros
            zero_count = sum(1 for v in momentum_factors.values() if v == 0.0)
            total_count = len(momentum_factors)
            
            print(f"\nImprovement Analysis:")
            print(f"  Zeroed factors: {zero_count}/{total_count}")
            if zero_count == 0:
                print("  🎉 All factors have meaningful values!")
            elif zero_count < total_count / 2:
                print("  ✅ Significant improvement from extraction fixes")
            else:
                print("  ⚠️ Still some zeroed factors - may need more work")
    
    except Exception as e:
        print(f"Error testing extraction: {e}")

def test_extraction_with_sample_data():
    """Test extraction with sample data patterns"""
    print("\n\n🔬 TESTING WITH SAMPLE DATA PATTERNS")
    print("=" * 60)
    
    analyzer = EnhancedGeminiAnalyzer()
    
    # Create sample pattern data similar to what we see in prediction history
    sample_patterns = {
        "UGO": {
            "momentum_intensity": "MomentumIntensity(intensity_score=8.3, duration_games=1, shift_trigger='love_hold, three_point_run', trend_direction='stable', confidence=0.0)",
            "pressure_metrics": "PressureMetrics(service_pressure_index=0.39, clutch_performance_rate=0.8, mental_fatigue_score=0.0, break_point_conversion_rate=0.0, pressure_response_pattern='clutch')",
            "serving_rhythm": "ServingRhythmMetrics(service_point_win_by_position={'15-0': 1.0, '30-0': 0.67}, recovery_from_0_15=1.0, service_consistency_score=7.6)",
            "games_held_percentage": 1.0,
            "current_momentum": "strong_serving",
            "pressure_situations_faced": 2,
            "pressure_situations_won": 2
        },
        "DAR": {
            "momentum_intensity": "MomentumIntensity(intensity_score=6.8, duration_games=2, shift_trigger='', trend_direction='stable', confidence=0.4)",
            "pressure_metrics": "PressureMetrics(service_pressure_index=0.0, clutch_performance_rate=0.5, mental_fatigue_score=0.0, break_point_conversion_rate=0.0, pressure_response_pattern='neutral')",
            "serving_rhythm": "ServingRhythmMetrics(service_point_win_by_position={'0-15': 1.0, '15-15': 1.0}, recovery_from_0_30=0.0, service_consistency_score=5.2)",
            "games_held_percentage": 0.75,
            "current_momentum": "neutral",
            "pressure_situations_faced": 0,
            "pressure_situations_won": 0
        }
    }
    
    print("Testing extraction with sample patterns:")
    
    for player_code, pattern in sample_patterns.items():
        print(f"\n{player_code} Sample Extraction:")
        print("-" * 35)
        
        momentum_intensity = analyzer._get_momentum_intensity(pattern)
        service_consistency = analyzer._get_service_consistency(pattern)
        mental_fatigue = analyzer._get_mental_fatigue(pattern)
        clutch_performance = analyzer._get_clutch_performance(pattern)
        
        print(f"  Momentum Intensity: {momentum_intensity:.1f} (expected: 8.3 for UGO, 6.8 for DAR)")
        print(f"  Service Consistency: {service_consistency:.1f} (expected: 7.6 for UGO, 5.2 for DAR)")
        print(f"  Mental Fatigue: {mental_fatigue:.1f}% (should be > 0)")
        print(f"  Clutch Performance: {clutch_performance:.1f}% (expected: 80% for UGO, 50% for DAR)")
    
    # Test momentum factors calculation
    momentum_factors = analyzer._extract_momentum_factors(sample_patterns, "UGO", "DAR")
    
    print(f"\nSample Momentum Factors:")
    print("-" * 25)
    for factor, value in momentum_factors.items():
        print(f"  {factor}: {value:.2f}")
    
    # Expected results
    expected_results = {
        'momentum_intensity_advantage': 8.3 - 6.8,  # 1.5
        'service_consistency_advantage': 7.6 - 5.2,  # 2.4
        'mental_fatigue_advantage': 0.0 - 0.0,      # Should be calculated from other factors
        'current_clutch_advantage': 80.0 - 50.0     # 30.0
    }
    
    print(f"\nExpected vs Actual:")
    print("-" * 20)
    for factor, expected in expected_results.items():
        actual = momentum_factors.get(factor, 0.0)
        status = "✅" if abs(actual - expected) < 0.1 or (expected == 0 and actual != 0) else "❌"
        print(f"  {factor}: {status} Expected: {expected:.1f}, Got: {actual:.1f}")

def main():
    """Run all tests"""
    test_extraction_methods()
    test_extraction_with_sample_data()
    
    print("\n\n✅ EXTRACTION TESTING COMPLETED")
    print("=" * 60)
    print("Enhanced extraction methods should now provide:")
    print("• Better momentum intensity extraction from string patterns")
    print("• Improved service consistency from serving rhythm data")
    print("• Enhanced mental fatigue calculation with fallbacks")
    print("• Better clutch performance extraction with multiple fallbacks")
    print("• Non-zero advantage calculations for learning system")

if __name__ == "__main__":
    main()
